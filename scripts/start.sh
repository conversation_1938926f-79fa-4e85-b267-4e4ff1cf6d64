#!/bin/bash

# Start script for development environment

echo "🚀 Starting Antsomi Generative AI API in Development Mode..."

# Check if .env.dev exists
if [ ! -f .env.dev ]; then
  echo "❌ .env.dev file not found!"
  echo "Please create .env.dev file with required environment variables."
  echo "You can copy from .env.dev.example if available."
  exit 1
fi

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
  echo "❌ Docker is not running!"
  echo "Please start Docker and try again."
  exit 1
fi

# Check if uv is installed
if ! command -v uv &>/dev/null; then
  echo "❌ uv is not installed!"
  echo "Please install uv first: curl -LsSf https://astral.sh/uv/install.sh | sh"
  exit 1
fi

# Start infrastructure services (Redis & PostgreSQL)
echo "🔨 Starting infrastructure services (Redis & PostgreSQL)..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for infrastructure services to start..."
sleep 5

# Check if services are running
echo "🔍 Checking infrastructure service status..."
docker-compose -f docker-compose.dev.yml ps

echo ""
echo "✅ Infrastructure services started successfully!"
echo "📋 Services available:"
echo "   - Redis: localhost:6379"
echo "   - PostgreSQL: localhost:5432"
echo ""
echo "🚀 Now you can start the application and worker manually:"
echo "   - Start app: uv run python run.py"
echo "   - Start worker: uv run celery -A celery_worker.celery worker --loglevel=INFO"
echo ""
echo "💡 Tip: Open multiple terminal windows to run app and worker simultaneously"
