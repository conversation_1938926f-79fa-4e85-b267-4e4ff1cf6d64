# Antsomi Generative AI API

[![The MIT License](https://img.shields.io/badge/license-MIT-orange.svg?style=flat-square)](LICENSE)

A production-grade Flask application for Generative AI services with OpenAI integration, built with modern Python tooling and containerized infrastructure.

# Contributing

We encourage you to contribute to this project! Please check out the [Contributing](CONTRIBUTING.md) guidelines about how to proceed.

# Getting Started

### Prerequisites

- Python 3.12 (required)
- Docker and <PERSON>er Compose
- [uv](https://docs.astral.sh/uv/) - Modern Python package manager

### Quick Setup

The easiest way to get started is using our automated setup script:

```bash
# Run the development setup script
./scripts/dev-setup.sh
```

This script will:

- Install `uv` if not already installed
- Install all dependencies
- Create `.env` file from template
- Set up log directory

### Manual Setup

If you prefer manual setup:

```bash
# Install uv (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies
uv sync

# Create environment file
cp .env.dev.example .env.dev
# Edit .env.dev and update API keys and configurations
```

### Configuration

The project supports multiple environments: `development`, `sandbox`, `staging`, and `production`. Configuration is managed through:

- Environment-specific `.env` files (`.env.dev`, `.env.staging`, etc.)
- YAML configuration files in `config/domain/{environment}/`

Key environment variables to configure in `.env.dev`:

```bash
APPLICATION_ENV=development
PORT=5001
API_KEY=your_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
BROKER_URL=redis://ai-redis:6379/0
RESULT_BACKEND=redis://ai-redis:6379/0
DATABASE_URL=**********************************************/genai
APP_NAME=antsomi-genai
```

### Running the Application

#### Option A: Using Shell Scripts (Recommended)

```bash
# Terminal 1: Start infrastructure services (Redis & PostgreSQL)
./scripts/start.sh

# Terminal 2: Start the Flask application
./scripts/start-app.sh

# Terminal 3: Start Celery worker (optional, for background tasks)
./scripts/start-worker.sh
```

#### Option B: Manual Commands

```bash
# Start infrastructure services
docker-compose -f docker-compose.dev.yml up -d

# Start Flask app
uv run python run.py

# Start Celery worker (in separate terminal)
uv run celery -A celery_worker.celery worker --loglevel=INFO
```

### Stopping Services

```bash
# Stop all services
./scripts/stop.sh
```

## Available Scripts

| Script | Description |
|--------|-------------|
| `./scripts/dev-setup.sh` | Initial development setup (install uv, dependencies, create .env) |
| `./scripts/start.sh` | Start infrastructure services (Redis, PostgreSQL) |
| `./scripts/start-app.sh` | Start the Flask application |
| `./scripts/start-worker.sh` | Start the Celery worker |
| `./scripts/stop.sh` | Stop infrastructure services |

## Services Information

### Infrastructure Services (Docker)

- **Redis**: `localhost:6379` - Message broker and caching
- **PostgreSQL**: `localhost:5432` - Database
  - Database: `genai`
  - User: `genai`
  - Password: `AMads@2024`

### Application Services (Host)

- **Flask App**: `http://localhost:5001` (or port specified in .env.dev)
- **Celery Worker**: Background task processing

## Troubleshooting

### Infrastructure not starting

```bash
# Check Docker status
docker info

# Check service logs
docker-compose -f docker-compose.dev.yml logs
```

### App/Worker connection issues

```bash
# Verify infrastructure is running
docker-compose -f docker-compose.dev.yml ps

# Check environment variables
cat .env.dev
```

### Port conflicts

- Check if ports 5432 (PostgreSQL) or 6379 (Redis) are already in use
- Modify `docker-compose.dev.yml` if needed

# Architecture & Technology Stack

## Core Dependencies

| Package | Usage |
|---------|-------|
| [Flask](https://flask.palletsprojects.com/) | Web framework |
| [OpenAI](https://platform.openai.com/docs) | AI/ML API integration |
| [Celery](https://docs.celeryproject.org/) | Background task processing |
| [Redis](https://redis.io/) | Caching and message broker |
| [Flask-CORS](https://flask-cors.readthedocs.io/) | Cross-Origin Resource Sharing |
| [python-dotenv](https://pypi.org/project/python-dotenv/) | Environment variable management |
| [marshmallow](https://marshmallow.readthedocs.io/) | Schema validation and serialization |
| [webargs](https://webargs.readthedocs.io/) | HTTP request parsing and validation |
| [tiktoken](https://github.com/openai/tiktoken) | Token counting for OpenAI models |

## Development Tools

- **Package Manager**: [uv](https://docs.astral.sh/uv/) - Fast Python package installer and resolver
- **Code Quality**: [ruff](https://docs.astral.sh/ruff/) - Fast Python linter and formatter
- **Containerization**: Docker & Docker Compose for infrastructure services

## Infrastructure

- **Redis**: Message broker and caching (Docker container)
- **PostgreSQL**: Database (Docker container)
- **Application**: Runs on host for development flexibility

# API Testing

Test if the application is running correctly using the following curl commands (or use Postman):

### Health Check

```bash
curl --location --request GET 'http://localhost:5001/status'
```

### Insight API (Example)

```bash
curl --location --request GET 'http://localhost:5001/api/v1/insight/test'
```

### Authenticated Endpoints

For endpoints requiring authentication, include the API key header:

```bash
curl --location --request GET 'http://localhost:5001/api/v1/insight/protected' \
  --header 'x-api-key: your_api_key_here'
```

**Note**: Replace `your_api_key_here` with the actual API key from your `.env.dev` file.

# Development Workflow

## Daily Development Process

1. **First time setup**: Run `./scripts/dev-setup.sh`
2. **Daily development**:
   - Start infrastructure: `./scripts/start.sh`
   - Start app: `./scripts/start-app.sh`
   - Start worker (if needed): `./scripts/start-worker.sh`
3. **End of day**: `./scripts/stop.sh` and Ctrl+C on app/worker terminals

## Benefits of This Approach

- **Faster development**: No need to rebuild Docker images for code changes
- **Better debugging**: Direct access to Python processes
- **Resource efficient**: Only infrastructure runs in containers
- **Flexible**: Easy to run individual components
- **Hot reload**: Code changes are immediately reflected

## Common Commands

```bash
# Install dependencies
uv sync

# Run linting
uv run ruff check .

# Format code
uv run ruff format .

# Run linting and formatting together
uv run ruff check --fix . && uv run ruff format .

# Start development environment
./scripts/dev-setup.sh && ./scripts/start.sh && ./scripts/start-app.sh
```

# License

 This program is free software under MIT license. Please see the [LICENSE](LICENSE) file in our repository for the full text.

