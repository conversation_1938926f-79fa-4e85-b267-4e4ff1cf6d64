import logging.config
from os import environ

from celery import Celery
from dotenv import load_dotenv
from flask import Flask, jsonify
from flask_cors import CORS

import openai

from .config import config as app_config

celery = Celery(__name__)


def create_app():
    load_dotenv()
    APPLICATION_ENV = get_environment()
    openai.api_key = app_config[APPLICATION_ENV].OPENAI_API_KEY
    logging.config.dictConfig(app_config[APPLICATION_ENV].LOGGING)
    app = Flask(app_config[APPLICATION_ENV].APP_NAME)
    app.config.from_object(app_config[APPLICATION_ENV])

    CORS(app, resources={r'/api/*': {'origins': '*'}})

    from .insight.apis import insight as insight_blueprint
    

    app.register_blueprint(
        insight_blueprint,
        url_prefix='/api/v1/insight'
    )

    return app


def get_environment():
    return environ.get('APPLICATION_ENV') or 'development'
