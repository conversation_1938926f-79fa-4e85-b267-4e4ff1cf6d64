
from flask import Blueprint, current_app, jsonify, request
from werkzeug.local import LocalProxy

from authentication import check_permission
from .business import suggest_question_openai, conversations, explore_data

insight = Blueprint('insight', __name__)
logger = LocalProxy(lambda: current_app.logger)


@insight.before_request
def before_request_func():
    current_app.logger.name = 'insights'


@insight.route('/explore-data', methods=['POST'])
@check_permission
def explore_data_api():
    body = request.json

    datasources = body.get("datasources")
    schemas = body.get("schemas")
    messages = body.get("messages")

    if not datasources:
        return jsonify({
            'code': 400,
            'message': 'datasources is required!',
            'data': ""
        })

    if not schemas:
        return jsonify({
            'code': 400,
            'message': 'schemas is required!',
            'data': ""
        })

    response = explore_data(datasources, schemas, messages)

    return jsonify({
        'code': 200,
        'message': 'Success',
        "type": response.get("type"),
        'data': response.get("content")
    })


@insight.route('/question', methods=['POST'])
@check_permission
def suggest_question():
    language = request.args.get('_lang', 'en')
    body = request.json
    chart_settings = body.get("chart_settings")

    if not chart_settings:
        return jsonify({
            'code': 400,
            'message': 'chart_settings is required!',
            'data': ""
        })

    questions = suggest_question_openai(chart_settings, language)

    return jsonify({
        'code': 200,
        'message': 'Success',
        'data': {
            'questions': questions
        }
    })


@insight.route('/conversation', methods=['POST'])
@check_permission
def conversation():
    language = request.args.get('_lang', 'en')
    body = request.json
    messages = body.get("messages")
    chart_settings = body.get("chart_settings")

    if not chart_settings:
        return jsonify({
            'code': 400,
            'message': 'chart_settings is required!',
            'data': ""
        })

    response = conversations(chart_settings, messages, language)

    if (response.get("status") == "ok"):
        return jsonify({
            'code': 200,
            'message': 'Success',
            'data': response.get("content")
        })

    return jsonify({
        'code': 400,
        'message': 'Failed',
        'data': ""
    })
