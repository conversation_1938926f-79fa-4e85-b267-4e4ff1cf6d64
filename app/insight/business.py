import openai
from ..constants import OPENAI_MODEL
import re
import tiktoken
import json


def num_tokens_from_string(string: str, encoding_name: str) -> int:
    """Returns the number of tokens in a text string."""
    encoding = tiktoken.get_encoding(encoding_name)
    num_tokens = len(encoding.encode(string))
    return num_tokens


def convertDataToCsvString(data):
    # string = ""
    # for num in data:
    #     string += ",".join(str(x) for x in num) + "\n"
    # return string
    return """
    Journey Name,Viewable,Clicks,CTR (Clicks/ Viewable),Num of Purchase,Total Revenue\n[LIVE] [PDP] Sản phẩm cùng phân khúc giá,94420,10629,0.11257148909129422,275,1444080000\n[Live] CDP - PDP- User Exit Intent - MEGA 19-23 Feb 2024,80077,4523,0.056483134982579265,144,838932000\n[LIVE] [PDP] <PERSON>ản phẩm dành riêng cho bạn,120468,4941,0.041015041338778764,228,726222000\n[Live] CDP - PDP- User Exit Intent - MEGA 02_02 30 Jan_03 Feb 2024,39874,3021,0.07576365551487185,82,479180000\n[LIVE] [Cate] Sản phẩm dành riêng cho bạn,57113,3956,0.06926619158510322,144,400757000\nBlast Campaign - [LIVE] [PDP] Sản phẩm cùng phân khúc giá,22025,2475,0.11237230419977298,46,276643000\n[Live] CDP - Search- User Exit Intent - MEGA 02_02 30 Jan_03 Feb 2024,6270,616,0.09824561403508772,27,192688000\nBlast Campaign - [LIVE] [Cate] Sản phẩm dành riêng cho bạn,14595,1003,0.06872216512504283,25,154827000\n[Live] CDP - FILTER- User Exit Intent - MEGA 19-23 Feb 2024,21432,1269,0.05921052631578947,12,124848000\n[Live] CDP - Search- User Exit Intent - MEGA 19-23 Feb 2024,10793,736,0.06819234689150375,34,122620000\nPDP Block Dynamic Product - PDP MDA 7-29 Feb 2024,10691,949,0.08876625198765317,17,106429000\n[LIVE] [blog] - Có thể bạn thích,110608,2287,0.020676623752350645,30,99171000\n[Live] CDP - CATE - User Exit Intent - MEGA 19-23 Feb 2024,23222,1518,0.06536904659374732,26,92741000\n[Live] CDP - CATE - User Exit Intent - MEGA 02_02 30 Jan_03 Feb 2024,11367,1024,0.09008533474091669,25,85215000\n[LIVE] [Blog-right menu],505187,1739,0.003442289686789248,24,69108000\nSlide in - Sticky SDA 11-31 Jan 2024,0,0,0,3,68670000\nSlide in - Sticky MDA 7-29 Feb 2024,84108,888,0.010557854187473249,8,64720000\nSlide in - Sticky SDA 7-29 Feb 2024,61271,704,0.01148993814365687,28,63503000\nBlast Campaign - [LIVE] [PDP] Sản phẩm dành riêng cho bạn,30748,1429,0.04647456745154156,32,57694000\n[Live] CDP - BLOG - User Exit Intent - MEGA 19-23 Feb 2024,206262,7738,0.03751539304379867,6,55720000\n[LIVE] Slide in - Blog - Sticky FESTIVAL - 18 Dec - 07 Jan 2023,28,1,0.03571428571428571,10,49069000\n[LIVE] Slide in - Blog - Sticky MEGA 9-12 Dec 2023,0,0,0,4,45660000\nPDP Block Dynamic Product - PDP SDA 7-29 Feb 2024,13076,1111,0.08496482104619149,49,44092000\nSlide in - Sticky MEGA 19-23 Feb 2024,33205,407,0.012257190182201476,16,42249000\nPDP Block Dynamic Product - PDP DIG 7-29 Feb 2024,5837,643,0.11015932842213466,12,40610000\n[Live] CDP - FILTER- User Exit Intent - MEGA 02_02 30 Jan_03 Feb 2024,10680,954,0.08932584269662922,9,40486000\nCate Block Dynamic Product - Cate MDA 7-29 Feb 2024,13548,690,0.05093002657218778,7,37860000\n[Live] CDP - BLOG - User Exit Intent - FESTIVAL - 18 Dec - 07 Jan 2023,1,3,3,4,32210000\nCate Block Dynamic Product - Cate MEGA 02_02 30 Jan_03 Feb 2024,5127,162,0.03159742539496782,6,31496000\nPDP Block Dynamic Product - PDP MDA 9-31 Jan 2024,1,0,0,2,27780000\nSlide in - Sticky MEGA 02_02 30 Jan_03 Feb 2024,32550,398,0.012227342549923196,2,25963000\nPDP Block Dynamic Product - PDP MEGA 19-23 Feb 2024,5394,314,0.0582128290693363,7,22378000\n[Live] CDP - CATE - User Exit Intent - Festival,0,0,0,5,20677000\n[LIVE] - Inline Blog - Dynamic Product - MDA 7-29 Feb 2024,316846,3922,0.012378253157685436,5,20237000\nPDP Block Dynamic Product - PDP BIGBANG 24-10 - 17-12 2023,0,0,0,2,19980000\nSlide in - Sticky MDA 14-23 Nov 2023,0,0,0,4,19456000\nSlide in - Sticky FESTIVAL - 18 Dec - 07 Jan 2023,0,0,0,3,16508000\n[LIVE] - Inline Blog - Dynamic Product - MEGA 19-23 Feb 2024.,55930,771,0.013785088503486502,1,16280000\nSlide in - Sticky SDA 8-23 Dec 2023,0,0,0,4,16060000\nPDP Block Dynamic Product - PDP MEGA 9-12 Dec 2023,0,0,0,2,14776000\nPDP Block Dynamic Product - PDP DIG AWO 18-Aug -7-Sep 2023,0,0,0,1,13340000\nCate Block Dynamic Product - Cate MDA 9-31 Jan 2024,0,0,0,2,13028000\n[LIVE] Slide in - Blog - MEGA 02_02 30 Jan_03 Feb 2024,73536,706,0.009600739773716276,4,12360000\n[Live] CDP - CATE - User Exit Intent - tet tay 1 Jan 2024,0,0,0,1,12168000\n[Live] CDP - BLOG - User Exit Intent - MEGA 02_02 30 Jan_03 Feb 2024,134325,7528,0.05604317885724921,2,12129000\n[Live] CDP - FILTER - User Exit Intent - MID MONTH 15-19 Jan 2024,0,0,0,4,11655000\nCate Block Dynamic Product - Cate MEGA 19-23 Feb 2024.,5623,173,0.030766494753690202,1,10689000\nPDP Block Dynamic Product - PDP SDA 11-31 Jan 2024,1,0,0,10,10018000\n[Live] CDP - PDP - User Exit Intent - MEGA 9-12 Dec 2023,0,0,0,2,9280000\n[LIVE] Slide in - Blog - Sticky BIGBANG,1,0,0,10,8738000\n
    """


def summary_questions(messages, question_user, language="en"):
    try:
        history_users = ""
        for message in messages:
            if message["role"] == "user":
                history_users += message["content"] + "\n"

        system_message = {
            "role": "system",
            "content": """
            Provide the chat history below and a follow-up question, rephrase the next input question into an independent question.

            Chat history:\"""
                {history}
            \"""

            Follow-up question: \"""
                {question}
            \"""

            Final question:
            """.format(history=history_users, question=question_user)
        }

        if (language == "vi"):
            system_message = {
                "role": "system",
                "content": """
                Đưa ra lịch sử trò chuyện sau đây và một câu hỏi tiếp theo, hãy diễn đạt lại câu hỏi đầu vào tiếp theo thành một câu hỏi độc lập.

                Lịch sử trò chuyện:\"""
                    {history}
                \"""

                Câu hỏi tiếp theo: \"""
                    {question}
                \"""

                Câu hỏi độc lập:
                """.format(history=history_users, question=question_user)
            }

        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[system_message],
            temperature=0,
            max_tokens=1000)

        # Extract the response from the API
        return response.choices[0].message.content
    except Exception as e:
        return ""


def suggest_question_openai(chart_settings, language="en"):
    try:
        chart_data = chart_settings.get('chart_data')
        data_source_name = chart_settings.get('data_source_name')

        system_message = {
            "role": "system",
            "content": """
            You are expert data analytics, and you will answer to my question. Before you start, please take a look at the dataset about {data_source_name} below
            ```{chart_data}```
            """.format(chart_data=chart_data, data_source_name=data_source_name)
        }

        user_message = {
            "role": "user",
            "content": """
            Provide me 10 questions that you think is worth exploring and can be answered.
            Just a question, no need to provide the answer, each questions should be in a new line.
            """
        }

        if (language == "vi"):
            system_message = {
                "role": "system",
                "content": """
                Bạn là một chuyên gia về phân tích dữ liệu, và bạn sẽ trả lời cho câu hỏi của tôi. Trước khi bạn bắt đầu, hãy xem xét tập dữ liệu về {data_source_name} dưới đây
                ```{chart_data}```
                """.format(chart_data=chart_data, data_source_name=data_source_name)
            }

            user_message = {
                "role": "user",
                "content": """
                Cho tôi 10 câu hỏi mà bạn nghĩ là đáng khám phá và có thể trả lời được. Chỉ cần câu hỏi, không cần cung cấp câu trả lời, mỗi câu hỏi nên ở dòng mới.
                """
            }

        messagesAPI = []
        messagesAPI.append(system_message)
        messagesAPI.append(user_message)

        # Call the OpenAI API to generate a response
        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=messagesAPI,
            temperature=0)

        # Extract the response from the API
        questions_api = response.choices[0].message.content

        questions_list = questions_api.split("\n")

        finally_questions = []

        regex = r"(^\d+\.\ )"
        pattern = re.compile(regex)
        for question in questions_list:
            format_question = pattern.sub("", question)
            finally_questions.append(format_question)

        return finally_questions
    except Exception as e:
        return []


def conversations(chart_settings, messages, language="en"):
    try:
        chart_data = chart_settings.get('chart_data')
        data_source_name = chart_settings.get('data_source_name')
        token_count = 0

        system_message = {
            "role": "system",
            "content": """
            You are expert data analytics, and you will answer to my question. Response do not exceed 500 words, remember to highlight(bold) important information such as journey name, viewable, click, CTR... . If data contains datetime, format to DD/MM/YYYY, and if it is a number, please format by adding commas for thousands separators and a decimal point. Before you start, please take a look at the dataset about {data_source_name} below
            ```{chart_data}```
            """.format(chart_data=chart_data, data_source_name=data_source_name)
        }

        if (language == "vi"):
            system_message = {
                "role": "system",
                "content": """
                Bạn là một chuyên gia về phân tích dữ liệu, và bạn sẽ trả lời cho câu hỏi của tôi. Trả lời không quá 500 từ. Nhớ phải highlight(in đậm) những thông tin quan trọng ví dụ như journey name, viewable, click, CTR... . Nếu dữ liệu có chứa ngày tháng, hãy định dạng lại theo DD/MM/YYYY, nếu nó là số, định dạng bằng cách thêm dấu phẩy và phần nghìn và dấu thập phân. Trước khi bạn bắt đầu, hãy xem xét tập dữ liệu về {data_source_name} dưới đây
                ```{chart_data}```
                """.format(chart_data=chart_data, data_source_name=data_source_name)
            }

        messagesAPI = []
        messagesAPI.append(system_message)

        token_count += num_tokens_from_string(
            system_message.get("content"), "cl100k_base")

        if isinstance(messages, list):
            if (len(messages) > 0):
                messages.reverse()
                message_stored = []
                for message in messages:
                    token_count += num_tokens_from_string(
                        message.get("content"), "cl100k_base")

                    if (token_count < 10000):
                        message_stored.append(message)

                message_stored.reverse()
                for message in message_stored:
                    messagesAPI.append(message)

                # latest_message = messages[-1]
                # messagesAPI.append(messages[-1])
                # for message in messages:
                #     if message["role"] == "user":
                #         messagesAPI.append(message)
            else:
                if (language == "vi"):
                    messagesAPI.append({
                        "role": "user",
                        "content": "Hãy đưa ra một số phân tích có ý nghĩa từ tập dữ liệu trên."
                    })
                else:
                    messagesAPI.append({
                        "role": "user",
                        "content": "Give some meanful analysis from the dataset above."
                    })
        print(messagesAPI)
        print(token_count)
        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=messagesAPI,
            temperature=0)

        # # Extract the response from the API
        return {
            "status": "ok",
            "content": response.choices[0].message.content
        }

    except Exception as e:
        raise e

        return {
            "status": "error",
            "content": "Try again later!"
        }


def explore_chart(object):
    print(object)


def explore_data(datasources, schemas, messages):
    try:
        data_source_name = datasources.get('name')
        columns = ""

        for schema in schemas:
            columns += "name: " + \
                schema.get('label') + ", data_type: " + \
                schema.get('dataType') + "\n"

        custom_functions = [
            {
                "name": "explore_chart",
                "description": "Function return a chart_type, dimensions, metric, date_range",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "chart_type": {"type": "string", "description": "type of chart"},
                        "dimensions": {"type": "string", "description": "list of dimensions to explore data"},
                        "metrics": {"type": "string", "description": "list of metrics to explore data"},
                        "date_range_column": {"type": "string", "description": "dimension with datatype is date"},
                    },
                    "required": ["chart_type", "dimensions", "metrics", "date_range_column"],
                },
            }
        ]

        system_message = {
            "role": "system",
            "content": """
            Tôi có một bảng dữ liệu tên là {data_source_name} gồm các cột như sau.
            ```{schema}```

            Bạn sẽ đóng vai trò như một chuyên gia về vẽ biểu đồ (chart), và nhiệm vụ của bạn là xác định loại biểu đồ thích hợp dựa trên câu hỏi của người dùng.
            Các loại chart mà hệ thống đang có là:
            'TABLE',
            'LINE_CHART',
            'BAR_CHART',
            'PIE_CHART',
            'GEO_MAP',
            'SCORE_CARD',
            'SCATTER_CHART',
            'BULLET_CHART',
            'AREA_CHART',
            'TREE_MAP',
            'PIVOT_CHART',
            'SUNBURST_CHART',
            'HEAT_MAP'.

            Khi người dùng yêu cầu vẽ biểu đồ, bạn cần xác định xem yêu cầu đó có hợp lý và dữ liệu có đủ thông tin để vẽ biểu đồ hay không. Nếu yêu cầu không hợp lý hoặc không phù hợp với việc vẽ biểu đồ, bạn có thể đặt lại câu hỏi cho người dùng để xác nhận yêu cầu chính xác nhất, tuy nhiên bạn phải đánh giá để có thể đưa ra chart phù hợp nhất, không cần người dùng phải đưa ra quá chi tiết cho loại chart nào.
            """.format(schema=columns, data_source_name=data_source_name)
        }

        messagesAPI = []
        messagesAPI.append(system_message)
        token_count = 0
        token_count += num_tokens_from_string(
            system_message.get("content"), "cl100k_base")

        if isinstance(messages, list):
            if (len(messages) > 0):
                messages.reverse()
                message_stored = []
                for message in messages:
                    token_count += num_tokens_from_string(
                        message.get("content"), "cl100k_base")

                    if (token_count < 5000):
                        message_stored.append(message)

                message_stored.reverse()
                for message in message_stored:
                    messagesAPI.append(message)

        print(messagesAPI)
        print(custom_functions)
        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=messagesAPI,
            functions=custom_functions,
            function_call="auto",
            temperature=0)

        print(response)
        print(response.choices[0].finish_reason)

        if (response.choices[0].finish_reason != 'function_call'):
            return {
                "status": "ok",
                "type": "messsage",
                "content": response.choices[0].message.content
            }
        else:
            function_args = response.choices[0].message.function_call.arguments

            arguments = json.loads(function_args)
            chart_type = arguments.get("chart_type")
            dimensions = arguments.get("dimensions")
            metrics = arguments.get("metrics")
            date_range = arguments.get("date_range_column")

            return {
                "status": "ok",
                "type": "chart",
                "content": {
                    "chart_type": chart_type,
                    "dimensions": dimensions,
                    "metrics": metrics,
                    "date_range": date_range
                }
            }
    except Exception as e:
        raise e
