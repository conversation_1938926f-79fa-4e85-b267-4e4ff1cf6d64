import os
import yaml


class Config:
    @staticmethod
    def get(file_name):
        if file_name:
            root_dir = os.path.abspath(os.curdir)            
            file_path = f"{root_dir}/config/autoload/{os.getenv('APPLICATION_ENV')}/{file_name}.yml"
            print(file_path)
            if os.path.exists(file_path):
                with open(file_path, "r") as file:
                    content = yaml.safe_load(file)
                    return content

            return None
