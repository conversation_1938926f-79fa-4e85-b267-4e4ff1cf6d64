import json
import requests
from app.library.redis import Redis
from config.common.constant import Constant


def validate(params):
    token = params.get("_token")

    result = {"code": 200, "message": "Success", "data": {}}

    if not token:
        return {"message": "Token not found!"}

    token_info = {
        "valid": True,
        "logout": False,
        "network_id": "",
        "manager_id": "",
        "allow_access": True,
        "token_expire": False,
    }

    redis = Redis.get_instances("caching")

    if not redis:
        raise Exception("Cannot get Redis instance")

    redis_key = f"caching_token:{token}"

    get_token_info = redis.get(redis_key)

    if not get_token_info:
        url = f"https://iam.ants.tech/api/token/validate"

        response = requests.post(
            url,
            json={"token": token},
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer iax1u7wwrn2mwj0ailh51fjkmt234yhe",
            },
        )
        data = response.json().get("data", {})
        get_token_info = data.get("data", None)

        if not get_token_info:
            return {
                **result,
                "data": {
                    "tokenInfo": {**token_info, "valid": False, "token_expire": True}
                },
            }

        get_token_info = json.dumps(get_token_info, separators=(",", ":"))
        redis.setex(redis_key, 5 * 60, get_token_info)

    get_token_info = json.loads(get_token_info)

    if get_token_info.get("access_level"):
        roles = [int(role) for role in get_token_info["access_level"].split("|")]

        if Constant.get("TOKEN_ROLE") in roles:
            if Constant.get("TOKEN_ROLE_ADMIN") in roles:
                result["allow_access"] = True

    token_info["manager_id"] = get_token_info["user_id"]
    token_info["network_id"] = get_token_info["network_id"]
    token_info["role"] = get_token_info["role"]

    return {**result, "data": {"token_info": token_info}}
