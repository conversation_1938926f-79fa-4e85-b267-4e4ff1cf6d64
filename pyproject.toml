[project]
name = "at.generative-ai"
version = "0.0.1"
requires-python = ">=3.12, <3.13"
dependencies = [
  "flask",
  "celery",
  "redis",
  "flask-cors",
  "python-dotenv",
  "marshmallow>=3.19.0",
  "webargs",
  "openai>=1.99.1",
  "tiktoken>=0.10.0",
  "pyyaml",
  "langchain-openai>=0.3.28",
  "pydantic>=2.11.7",
  "chromadb>=1.0.16",
  "psycopg[binary,pool]>=3.2.9",
  "colorlog>=6.9.0",
]

[dependency-groups]
dev = ["ruff", "ipython"]

[tool.uv]
package = false

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple"

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
  ".bzr",
  ".direnv",
  ".eggs",
  ".git",
  ".git-rewrite",
  ".hg",
  ".mypy_cache",
  ".nox",
  ".pants.d",
  ".pytype",
  ".ruff_cache",
  ".svn",
  ".tox",
  ".venv",
  "__pypackages__",
  "_build",
  "buck-out",
  "build",
  "dist",
  "node_modules",
  "venv",
]


# Same as Black.
line-length = 80
indent-width = 4

# Assume Python 3.12
target-version = "py312"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
