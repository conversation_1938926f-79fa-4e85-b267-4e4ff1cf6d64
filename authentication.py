from functools import wraps

from flask import abort, current_app, request, jsonify

from werkzeug.datastructures import MultiDict

from app.token.business import validate


def authenticated_request():
    try:
        user_id = request.args.get("_user_id")
        token = request.args.get("_token")
        # account_id = request.args.get("_account_id")

        if token and user_id:
            check_token = validate({"_token": token})

            token_info = check_token.get("data", {}).get("token_info", None)

            if token_info["valid"]:
                if not token_info["allow_access"]:
                    return False
                elif not user_id:
                    return False
                else:
                    args = request.args.to_dict()

                    args["user_id"] = user_id
                    args["network_id"] = int(token_info.get("network_id", None))
                    args["manager_id"] = int(token_info.get("manager_id", None))
                    args["token"] = token

                    request.args = args
                    return True

            return False
        else:
            return False
    except Exception as e:
        print("Error: ", e)
        return False


def check_permission(view_function):
    @wraps(view_function)
    # the new, post-decoration function. Note *args and **kwargs here.
    def decorated_function(*args, **kwargs):
        if authenticated_request():
            return view_function(*args, **kwargs)
        else:
            abort(
                jsonify(
                    code=401,
                    message="You do not have permission to perform this request",
                ),
                401,
            )

    return decorated_function
