FROM python:3.12-slim-bookworm

RUN apt-get update && apt-get install -y \
  python3-dev \
  libpq-dev \
  gcc \
  vim \
  curl \
  wget \
  net-tools

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Create app directory
WORKDIR /app

ENV APPLICATION_ENV=sandbox

# Copy uv configuration files
COPY pyproject.toml uv.lock ./

# Install dependencies using uv
RUN uv sync --locked

# Bundle app source
COPY . /app

EXPOSE ${PORT}
CMD ["uv", "run", "python", "run.py"]
