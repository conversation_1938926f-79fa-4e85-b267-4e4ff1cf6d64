## Contributing Guidelines

Flask Boilerplate is Open Source! I love it when people contribute!

Remember that communication is the lifeblood of any Open Source project. We are all working on this together, and we are all benefiting from this software. It's very easy to misunderstand one another over asynchronous, text-based conversations: When in doubt, assume everyone you're interacting within this project has the best intentions.

### Create an issue

Nobody's perfect. Something doesn't work? Something could be done better? Check to see if the issue already exists, and if it does, leave a comment to get our attention! If the issue doesn't already exist, feel free to create a new one.

### How to contribute

1. Fork the project and clone it to your local machine. Follow the initial setup as mentioned in the `README.md`
2. Create a branch with your GitHub username as a prefix and the ID of the
   issue as a suffix, for example: `git checkout -b USERNAME/that-new-feature-1234` or `git checkout -b USERNAME/fixing-that-bug-1234` where `USERNAME` should be replaced by your username and `1234` is the ID of the issue tied to your pull request. If there is no issue, you can leave the number out.
3. Code and commit your changes. Bonus points if you write a [good commit message](https://chris.beams.io/posts/git-commit/): `git commit -m 'Add some feature'`.
4. Push to the branch: `git push -u origin USERNAME/that-new-feature-1234`.
5. Create a pull request for your branch. 🎉

We strongly encourage creating an issue before working on a PR! Try to keep the pull requests small. Please note that the core team will prioritize PRs that solve existing issues. 

### Code Style

Flask Boilerplate follows [flake8](https://pypi.org/project/flake8/) linting for python. We recommend using [autopep8](https://pypi.org/project/autopep8/) for auto formatting the python code. Please note that if your code is not formatted according to the guide as much as possible, we will reject your Pull Request until it is fixed. 


### The bottom line

We are all humans trying to work together to improve the community. Always be kind and appreciate the need for tradeoffs. ❤️
