services:
  ai-redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    restart: always

  ai-postgres:
    image: postgres:12.1
    environment:
      - POSTGRES_DB=genai
      - POSTGRES_USER=genai
      - POSTGRES_PASSWORD=AMads@2024
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: always

  # app-sandbox:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile-sandbox
  #   env_file:
  #     - .env.dev
  #   ports:
  #     - "8000:8000"
  #   volumes:
  #     - .:/app
  #   depends_on:
  #     - ai-redis
  #     - ai-postgres
  #   restart: always

volumes:
  redis-data:
  postgres-data:
